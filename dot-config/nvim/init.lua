
-- ============================================================================
-- NEOVIM CONFIGURATION
-- ============================================================================

do -- Leader Key Configuration
    -- Set <SPACE> as the `global` leader key, <COMMA> as `local` leader key
    -- NOTE: Must happen before plugins are required (otherwise wrong leader will be used)
    vim.g.mapleader = ' '
    vim.g.maplocalleader = ','
end

do -- Plugin Installation and Management
    do -- echasnovski/mini.nvim bootstrap
        local path_package = vim.fn.stdpath('data') .. '/site'
        local mini_path = path_package .. '/pack/deps/start/mini.nvim'
        if not vim.loop.fs_stat(mini_path) then
        vim.cmd('echo "Installing `mini.nvim`" | redraw')
        local clone_cmd = {
            'git', 'clone', '--filter=blob:none',
            -- Uncomment next line to use 'stable' branch
            -- '--branch', 'stable',
            'https://github.com/echasnovski/mini.nvim', mini_path
        }
        vim.fn.system(clone_cmd)
        vim.cmd('packadd mini.nvim | helptags ALL')
        vim.cmd('echo "Installed `mini.nvim`" | redraw')
        end
    end

    vim.pack.add({ -- Plugin declarations
        { src = "https://github.com/vague2k/vague.nvim" },
        { src = "https://github.com/echasnovski/mini.nvim" },
        { src = "https://github.com/stevearc/oil.nvim" },
        { src = "https://github.com/nvim-lua/plenary.nvim" }, -- Required for telescope
        { src = "https://github.com/nvim-telescope/telescope.nvim" },
        { src = "https://github.com/nvim-telescope/telescope-fzf-native.nvim" }, -- Optional but recommended
        { src = "https://github.com/nvim-treesitter/nvim-treesitter" },
        { src = "https://github.com/nvim-treesitter/nvim-treesitter-textobjects" },
        { src = "https://github.com/neovim/nvim-lspconfig" },
        { src = "https://github.com/VonHeikemen/lsp-zero.nvim" },
        { src = "https://github.com/williamboman/mason.nvim" },
        { src = "https://github.com/williamboman/mason-lspconfig.nvim" },
        { src = "https://github.com/hrsh7th/nvim-cmp" },
        { src = "https://github.com/hrsh7th/cmp-buffer" },
        { src = "https://github.com/hrsh7th/cmp-path" },
        { src = "https://github.com/hrsh7th/cmp-nvim-lsp" },
        { src = "https://github.com/hrsh7th/cmp-nvim-lua" },
        { src = "https://github.com/L3MON4D3/LuaSnip" },
        { src = "https://github.com/saadparwaiz1/cmp_luasnip" },
        { src = "https://github.com/ThePrimeagen/harpoon" },
        { src = "https://github.com/mbbill/undotree" },
        { src = "https://github.com/tpope/vim-fugitive" },
        { src = "https://github.com/chomosuke/typst-preview.nvim" },
        { src = "https://github.com/saghen/blink.cmp", checkout = "v0.*" }, -- Modern completion engine with version pin
        -- UI and Dashboard
        { src = "https://github.com/folke/snacks.nvim" }, -- Modern UI components and dashboard
        { src = "https://github.com/folke/which-key.nvim" }, -- Key mapping helper
        { src = "https://github.com/MunifTanjim/nui.nvim" }, -- Required for Laravel.nvim
        { src = "https://github.com/kevinhwang91/promise-async" }, -- Required for Laravel.nvim

        -- Note taking and organization
        { src = "https://github.com/nvim-neorg/neorg" }, -- Note taking and organization (re-enabled with safer config)

        -- Telescope extensions
        { src = "https://github.com/jvgrootveld/telescope-zoxide" }, -- Zoxide integration
        { src = "https://github.com/nvim-neorg/neorg-telescope" }, -- Neorg telescope integration (re-enabled)
        { src = "https://github.com/gbirke/telescope-foldmarkers.nvim" }, -- Fold markers search (corrected URL)
        { src = "https://github.com/zschreur/telescope-jj.nvim" }, -- Jujutsu VCS integration (corrected URL)
        { src = "https://github.com/nvim-telescope/telescope-github.nvim" }, -- GitHub integration
        { src = "https://github.com/nvim-telescope/telescope-media-files.nvim" }, -- Media files preview
        { src = "https://github.com/nvim-telescope/telescope-fzf-writer.nvim" }, -- FZF writer
        { src = "https://github.com/nvim-telescope/telescope-symbols.nvim" }, -- Symbol picker
        { src = "https://github.com/olacin/telescope-cc.nvim" }, -- Conventional commits (corrected URL)
        { src = "https://github.com/sudormrfbin/cheatsheet.nvim" }, -- Cheatsheet
        { src = "https://github.com/nat-418/telescope-color-names.nvim" }, -- Color names (corrected URL)
        { src = "https://github.com/octarect/telescope-menu.nvim" }, -- Menu system
        { src = "https://github.com/debugloop/telescope-undo.nvim" }, -- Undo tree

        -- Debugging
        { src = "https://github.com/mfussenegger/nvim-dap" }, -- Debug adapter protocol
        { src = "https://github.com/nvim-telescope/telescope-dap.nvim" }, -- DAP telescope integration
        -- Themes
        { src = "https://github.com/catppuccin/nvim" },
        { src = "https://github.com/rebelot/kanagawa.nvim" },

        -- Laravel Development
        { src = "https://github.com/adalessa/laravel.nvim" }, -- Comprehensive Laravel plugin
        { src = "https://github.com/ricardoramirezr/blade-nav.nvim" }, -- Blade navigation and completion
        { src = "https://github.com/jwalton512/vim-blade" }, -- Blade syntax highlighting

        -- AI Assistance
        { src = "https://github.com/github/copilot.vim" }, -- GitHub Copilot
        { src = "https://github.com/augmentcode/augment.vim" }, -- Augment AI code suggestions
        { src = "https://github.com/Exafunction/codeium.nvim" }, -- Local AI and cloud AI support

        -- LM Studio / Local LLM Integration
        { src = "https://github.com/olimorris/codecompanion.nvim" }, -- AI coding companion with local LLM support
        { src = "https://github.com/David-Kunz/gen.nvim" }, -- Local AI text generation plugin
    })
end

do -- Telescope Configuration
    require('telescope').setup({
        defaults = {
            -- Default configuration for telescope goes here:
            mappings = {
                i = {
                    -- Insert mode mappings
                    ["<C-n>"] = "move_selection_next",
                    ["<C-p>"] = "move_selection_previous",
                    ["<C-c>"] = "close",
                    ["<C-j>"] = "move_selection_next",
                    ["<C-k>"] = "move_selection_previous",
                },
                n = {
                    -- Normal mode mappings
                    ["<esc>"] = "close",
                    ["j"] = "move_selection_next",
                    ["k"] = "move_selection_previous",
                    ["q"] = "close",
                },
            },
        },
        pickers = {
            find_files = {
                theme = "dropdown",
            }
        },
        extensions = {
            -- FZF native extension
            fzf = {
                fuzzy = true,
                override_generic_sorter = true,
                override_file_sorter = true,
                case_mode = "smart_case",
            },
            -- Media files extension
            media_files = {
                filetypes = {"png", "webp", "jpg", "jpeg"},
                find_cmd = "rg"
            },
            -- Undo extension
            undo = {
                use_delta = true,
                use_custom_command = nil,
                side_by_side = false,
                vim_diff_opts = { ctxlen = vim.o.scrolloff },
                entry_format = "state #$ID, $STAT, $TIME",
                time_format = "",
                saved_only = false,
            },
            -- Zoxide extension
            zoxide = {
                prompt_title = "[ Walking on the shoulders of TJ ]",
                mappings = {
                    default = {
                        after_action = function(selection)
                            print("Update to (" .. selection.z_score .. ") " .. selection.path)
                        end
                    },
                    ["<C-s>"] = {
                        before_action = function(selection) print("before C-s") end,
                        action = function(selection)
                            vim.cmd.edit(selection.path)
                        end
                    },
                    ["<C-q>"] = { action = "file_vsplit" },
                },
            },
        }
    })

    do -- Load telescope extensions
        pcall(require('telescope').load_extension, 'fzf')
        pcall(require('telescope').load_extension, 'zoxide')
        pcall(require('telescope').load_extension, 'neorg')
        pcall(require('telescope').load_extension, 'foldmarkers') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'jj') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'gh')
        pcall(require('telescope').load_extension, 'media_files')
        pcall(require('telescope').load_extension, 'fzf_writer')
        pcall(require('telescope').load_extension, 'symbols')
        pcall(require('telescope').load_extension, 'conventional_commits') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'cheatsheet')
        pcall(require('telescope').load_extension, 'color_names') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'menu')
        pcall(require('telescope').load_extension, 'undo')
        pcall(require('telescope').load_extension, 'dap')
    end

    do -- Telescope key mappings
        local builtin = require('telescope.builtin')

        -- Core telescope mappings
        vim.keymap.set('n', '<leader>ff', builtin.find_files, { desc = 'Find files' })
        vim.keymap.set('n', '<leader>fg', builtin.live_grep, { desc = 'Live grep' })
        vim.keymap.set('n', '<leader>fb', builtin.buffers, { desc = 'Buffers' })
        vim.keymap.set('n', '<leader>fh', builtin.help_tags, { desc = 'Help tags' })
        vim.keymap.set('n', '<leader>fr', builtin.oldfiles, { desc = 'Recent files' })
        vim.keymap.set('n', '<leader>fs', builtin.current_buffer_fuzzy_find, { desc = 'Search in current buffer' })
        vim.keymap.set('n', '<leader>fc', builtin.commands, { desc = 'Commands' })
        vim.keymap.set('n', '<leader>fk', builtin.keymaps, { desc = 'Keymaps' })
        vim.keymap.set('n', '<leader>f', builtin.find_files, { desc = 'Find files' })
        vim.keymap.set('n', '<leader>h', builtin.help_tags, { desc = 'Help tags' })

        -- Extension mappings
        vim.keymap.set('n', '<leader>tz', '<cmd>Telescope zoxide list<cr>', { desc = 'Zoxide' })
        vim.keymap.set('n', '<leader>tu', '<cmd>Telescope undo<cr>', { desc = 'Undo tree' })
        vim.keymap.set('n', '<leader>tm', '<cmd>Telescope media_files<cr>', { desc = 'Media files' })
        vim.keymap.set('n', '<leader>ts', '<cmd>Telescope symbols<cr>', { desc = 'Symbols' })
        vim.keymap.set('n', '<leader>tc', '<cmd>Telescope cheatsheet<cr>', { desc = 'Cheatsheet' })
        vim.keymap.set('n', '<leader>tC', '<cmd>Telescope color_names<cr>', { desc = 'Color names' }) -- Re-enabled with correct URL
        vim.keymap.set('n', '<leader>tM', '<cmd>Telescope menu<cr>', { desc = 'Menu' })
        vim.keymap.set('n', '<leader>tj', '<cmd>Telescope jj<cr>', { desc = 'Jujutsu VCS' }) -- New mapping for Jujutsu
        vim.keymap.set('n', '<leader>tf', '<cmd>Telescope foldmarkers<cr>', { desc = 'Fold markers' }) -- Re-enabled with correct URL
        vim.keymap.set('n', '<leader>tcc', '<cmd>Telescope conventional_commits<cr>', { desc = 'Conventional commits' }) -- New mapping

        -- Git/GitHub mappings
        vim.keymap.set('n', '<leader>gi', '<cmd>Telescope gh issues<cr>', { desc = 'GitHub issues' })
        vim.keymap.set('n', '<leader>gp', '<cmd>Telescope gh pull_request<cr>', { desc = 'GitHub PRs' })
        vim.keymap.set('n', '<leader>gr', '<cmd>Telescope gh run<cr>', { desc = 'GitHub runs' })

        -- Debug mappings
        vim.keymap.set('n', '<leader>dc', '<cmd>Telescope dap commands<cr>', { desc = 'DAP commands' })
        vim.keymap.set('n', '<leader>db', '<cmd>Telescope dap list_breakpoints<cr>', { desc = 'DAP breakpoints' })
        vim.keymap.set('n', '<leader>dv', '<cmd>Telescope dap variables<cr>', { desc = 'DAP variables' })
        vim.keymap.set('n', '<leader>df', '<cmd>Telescope dap frames<cr>', { desc = 'DAP frames' })

        -- Neorg mappings
        vim.keymap.set('n', '<leader>nf', '<cmd>Telescope neorg find_linkable<cr>', { desc = 'Find linkable' })
        vim.keymap.set('n', '<leader>nh', '<cmd>Telescope neorg search_headings<cr>', { desc = 'Search headings' })
        vim.keymap.set('n', '<leader>ni', '<cmd>Telescope neorg insert_link<cr>', { desc = 'Insert link' })
        vim.keymap.set('n', '<leader>nF', '<cmd>Telescope neorg insert_file_link<cr>', { desc = 'Insert file link' })
    end
end

do -- Editor Options and Settings
    vim.o.clipboard = "unnamedplus"
    vim.o.expandtab = true
    vim.o.number = true
    vim.o.relativenumber = true
    vim.o.signcolumn = "yes"
    vim.o.smartindent = true
    vim.o.softtabstop = 4
    vim.o.swapfile = false
    vim.o.tabstop = 4
    vim.o.termguicolors = true
    vim.o.winborder = "rounded"
    vim.o.wrap = true
end

do -- Key Mappings
    -- File operations
    vim.keymap.set('n', '<leader>o', ':update<CR> :source<CR>')
    vim.keymap.set('n', '<leader>w', ':write<CR>')
    vim.keymap.set('n', '<leader>q', ':quit<CR>')

    -- Clipboard operations
    vim.keymap.set({ 'n', 'v', 'x' }, '<leader>y', '"+y<CR>')
    vim.keymap.set({ 'n', 'v', 'x' }, '<leader>d', '"+d<CR>')

    -- File explorer
    vim.keymap.set('n', '<leader>e', ":Oil<CR>")

    -- LSP mappings
    vim.keymap.set('n', '<leader>lf', vim.lsp.buf.format)
end

do -- LSP Configuration
    vim.api.nvim_create_autocmd('LspAttach', {
        callback = function(ev)
            -- Blink.cmp handles completion, so we don't need to enable native completion
            local client = vim.lsp.get_client_by_id(ev.data.client_id)

            -- Optional: Add LSP-specific keymaps here
            local opts = { buffer = ev.buf }
            vim.keymap.set('n', 'gd', vim.lsp.buf.definition, opts)
            vim.keymap.set('n', 'K', vim.lsp.buf.hover, opts)
            vim.keymap.set('n', 'gi', vim.lsp.buf.implementation, opts)
            vim.keymap.set('n', '<C-k>', vim.lsp.buf.signature_help, opts)
            vim.keymap.set('n', '<leader>rn', vim.lsp.buf.rename, opts)
            vim.keymap.set('n', '<leader>ca', vim.lsp.buf.code_action, opts)
            vim.keymap.set('n', 'gr', vim.lsp.buf.references, opts)
        end,
    })

    -- Manual PHP LSP configuration for Devsense PHP LS
    vim.api.nvim_create_autocmd("FileType", {
        pattern = "php",
        callback = function()
            vim.lsp.start({
                name = "devsense-php-ls",
                cmd = { "devsense-php-ls" }, -- Adjust this if the command is different
                root_dir = vim.fs.dirname(vim.fs.find({"composer.json", ".git"}, { upward = true })[1]),
                settings = {
                    php = {
                        completion = {
                            enabled = true,
                        },
                        diagnostics = {
                            enabled = true,
                        },
                        format = {
                            enabled = true,
                        },
                    },
                },
            })
        end,
    })

    -- Enable LSP servers
    vim.lsp.enable({ "lua_ls", "biome", "tinymist", "emmetls", "phpactor" })
end

do -- Plugin Configurations

    -- Load essential plugins first
    vim.cmd('packadd plenary.nvim')
    vim.cmd('packadd nui.nvim')
    vim.cmd('packadd promise-async')

    -- Build blink.cmp from source if needed
    do -- Blink.cmp build check and setup
        local blink_path = vim.fn.stdpath('data') .. '/site/pack/deps/start/blink.cmp'
        local target_dir = blink_path .. '/target/release'

        -- Force rebuild if checkout version exists (cleanup old version)
        if vim.fn.isdirectory(blink_path) == 1 then
            local git_cmd = 'cd ' .. blink_path .. ' && git describe --tags 2>/dev/null'
            local current_ref = vim.fn.system(git_cmd)
            if string.match(current_ref, 'v0%.') then
                vim.cmd('echo "Removing old blink.cmp version..." | redraw')
                vim.fn.delete(blink_path, 'rf')
            end
        end

        -- Check if binary exists, if not build it
        if vim.fn.isdirectory(blink_path) == 1 and vim.fn.isdirectory(target_dir) == 0 then
            vim.cmd('echo "Building blink.cmp from source..." | redraw')
            local build_cmd = 'cd ' .. blink_path .. ' && cargo build --release'
            local result = vim.fn.system(build_cmd)
            if vim.v.shell_error == 0 then
                vim.cmd('echo "blink.cmp built successfully" | redraw')
            else
                vim.cmd('echo "Failed to build blink.cmp: ' .. result .. '" | redraw')
            end
        end
    end

    do -- Debug Adapter Protocol (DAP) configuration
        local dap = require('dap')

        -- Basic DAP configuration for common languages
        -- JavaScript/TypeScript (Node.js)
        dap.adapters.node2 = {
            type = 'executable',
            command = 'node',
            args = { vim.fn.stdpath('data') .. '/mason/packages/node-debug2-adapter/out/src/nodeDebug.js' },
        }

        dap.configurations.javascript = {
            {
                name = 'Launch',
                type = 'node2',
                request = 'launch',
                program = '${file}',
                cwd = vim.fn.getcwd(),
                sourceMaps = true,
                protocol = 'inspector',
                console = 'integratedTerminal',
            },
        }

        dap.configurations.typescript = dap.configurations.javascript
    end

    do -- Laravel configuration
        -- Load Laravel.nvim dependencies first
        vim.cmd('packadd nui.nvim')
        vim.cmd('packadd promise-async')

        -- Laravel.nvim configuration
        require("laravel").setup({
            lsp_server = "phpactor", -- Use phpactor as LSP server
            -- Use snacks picker since you have snacks.nvim configured
            default_picker = "snacks",
            -- Alternatively, use telescope since you have extensive telescope setup
            -- default_picker = "telescope",
        })

        -- Blade-nav.nvim configuration
        require("blade-nav").setup({
            -- This setting works for blink.cmp
            close_tag_on_complete = true, -- default: true
        })

        do -- Laravel key mappings
            -- Artisan commands
            vim.keymap.set('n', '<leader>aa', '<cmd>Laravel artisan<cr>', { desc = 'Artisan commands' })
            vim.keymap.set('n', '<leader>ar', '<cmd>Laravel routes<cr>', { desc = 'Laravel routes' })
            vim.keymap.set('n', '<leader>am', '<cmd>Laravel make<cr>', { desc = 'Laravel make' })

            -- Laravel development
            vim.keymap.set('n', '<leader>av', '<cmd>Laravel view_finder<cr>', { desc = 'View finder' })
            vim.keymap.set('n', '<leader>ac', '<cmd>Laravel composer<cr>', { desc = 'Composer commands' })
            vim.keymap.set('n', '<leader>at', '<cmd>Laravel tinker<cr>', { desc = 'Laravel Tinker' })

            -- Route navigation (blade-nav provides this)
            vim.keymap.set('n', '<leader>ao', '<cmd>Laravel route:open<cr>', { desc = 'Open route in browser' })
        end
    end

    do -- Neorg configuration with safe loading to avoid circular dependencies
        pcall(function()
            -- Only load neorg if it's available and properly installed
            local neorg_ok, neorg = pcall(require, "neorg")
            if neorg_ok then
                neorg.setup({
                    load = {
                        ["core.defaults"] = {},
                        ["core.concealer"] = {
                            config = {
                                icon_preset = "basic", -- Use basic icons to avoid font issues
                            }
                        },
                        ["core.dirman"] = {
                            config = {
                                workspaces = {
                                    notes = "~/notes",
                                    work = "~/work-notes",
                                },
                                default_workspace = "notes",
                            }
                        },
                        -- Skip completion module to avoid conflicts with blink.cmp
                        -- ["core.completion"] = {
                        --     config = {
                        --         engine = "nvim-cmp",
                        --     }
                        -- },
                        ["core.integrations.telescope"] = {},
                    },
                })
            end
        end)
    end

    -- Oil file manager
    require "oil".setup()

    -- Snacks.nvim - Modern UI components and comprehensive dashboard
    do -- Dashboard configuration with random selection
        -- Custom startup section for vim.pack
        local function get_pack_stats()
            local start_time = vim.g.start_time or vim.fn.reltime()
            local current_time = vim.fn.reltime()
            local startup_time = vim.fn.reltimestr(vim.fn.reltime(start_time, current_time))

            -- Count loaded plugins from vim.pack
            local plugin_count = 0
            if vim.pack and vim.pack.list then
                for _ in pairs(vim.pack.list()) do
                    plugin_count = plugin_count + 1
                end
            end

            return {
                { "⚡ Neovim loaded in " .. string.format("%.2f", tonumber(startup_time) * 1000) .. "ms", hl = "footer" },
                { "📦 " .. plugin_count .. " plugins loaded", hl = "footer" },
            }
        end

        -- Define the dashboard specs
        local dashboard_specs = {
            -- Original dashboard spec
            {
                sections = {
                    { section = "header" },
                    { section = "keys", gap = 1, padding = 1 },
                    { section = "terminal", cmd = "curl -s 'wttr.in/?0'"},
                    { text = get_pack_stats, padding = 1 },
                },
            },
            -- First new dashboard spec
            {
                sections = {
                    { section = "header" },
                    {
                        pane = 2,
                        section = "terminal",
                        cmd = "colorscript -e square",
                        height = 5,
                        padding = 1,
                    },
                    { section = "keys", gap = 1, padding = 1 },
                    { pane = 2, icon = " ", title = "Recent Files", section = "recent_files", indent = 2, padding = 1 },
                    { pane = 2, icon = " ", title = "Projects", section = "projects", indent = 2, padding = 1 },
                    {
                        pane = 2,
                        icon = " ",
                        title = "Git Status",
                        section = "terminal",
                        enabled = function()
                            return Snacks.git.get_root() ~= nil
                        end,
                        cmd = "git status --short --branch --renames",
                        height = 5,
                        padding = 1,
                        ttl = 5 * 60,
                        indent = 3,
                    },
                    { section = "terminal", cmd = "curl -s 'wttr.in/?0'"},
                    { text = get_pack_stats, padding = 1 },
                },
            },
            -- Second new dashboard spec with random image
            {
                sections = {
                    {
                        section = "terminal",
                        cmd = "chafa \"$(find ~/nc/Pictures/wallpapers/Dynamic-Wallpapers/Dark -type f \\( -iname \"*.jpg\" -o -iname \"*.jpeg\" -o -iname \"*.png\" -o -iname \"*.gif\" \\) | shuf -n 1)\" --format symbols --symbols vhalf --size 60x17 --stretch; sleep .1",
                        height = 17,
                        padding = 1,
                    },
                    {
                        pane = 2,
                        { section = "keys", gap = 1, padding = 1 },
                        { section = "terminal", cmd = "curl -s 'wttr.in/?0'"},
                        { text = get_pack_stats, padding = 1 },
                    },
                },
            },
            -- Fourth dashboard spec - Pokemon colorscripts
            {
                sections = {
                    { section = "header" },
                    { section = "keys", gap = 1, padding = 1 },
                    { section = "terminal", cmd = "curl -s 'wttr.in/?0'"},
                    { text = get_pack_stats, padding = 1 },
                    {
                        section = "terminal",
                        cmd = "pokemon-colorscripts -r --no-title; sleep .1",
                        random = 10,
                        pane = 2,
                        indent = 4,
                        height = 30,
                    },
                },
            },
            -- Fifth dashboard spec - Fortune with cowsay and comprehensive sections
            {
                formats = {
                    key = function(item)
                        return { { "[", hl = "special" }, { item.key, hl = "key" }, { "]", hl = "special" } }
                    end,
                },
                sections = {
                    { section = "terminal", cmd = "fortune -s | cowsay", hl = "header", padding = 1, indent = 8 },
                    { title = "MRU", padding = 1 },
                    { section = "recent_files", limit = 8, padding = 1 },
                    { title = "MRU ", file = vim.fn.fnamemodify(".", ":~"), padding = 1 },
                    { section = "recent_files", cwd = true, limit = 8, padding = 1 },
                    { title = "Sessions", padding = 1 },
                    { section = "projects", padding = 1 },
                    { title = "Bookmarks", padding = 1 },
                    { section = "keys" },
                },
            },
        }

        -- Randomly select one of the dashboard specs
        math.randomseed(os.time())
        local selected_spec = dashboard_specs[math.random(#dashboard_specs)]

        local header_specs = {
            -- Orginal snax header
            { header_spec = table.concat({
                    [[                                                           ]],
                    [[.  ███╗   ██╗███████╗ ██████╗ ██╗   ██╗██╗███╗   ███╗      ]],
                    [[.  ████╗  ██║██╔════╝██╔═══██╗██║   ██║██║████╗ ████║      ]],
                    [[.  ██╔██╗ ██║█████╗  ██║   ██║██║   ██║██║██╔████╔██║      ]],
                    [[.  ██║╚██╗██║██╔══╝  ██║   ██║╚██╗ ██╔╝██║██║╚██╔╝██║      ]],
                    [[.  ██║ ╚████║███████╗╚██████╔╝ ╚████╔╝ ██║██║ ╚═╝ ██║      ]],
                    [[.  ╚═╝  ╚═══╝╚══════╝ ╚═════╝   ╚═══╝  ╚═╝╚═╝     ╚═╝      ]],
                    [[                                                           ]],
                }, '\n'),
            },
            { header_spec = table.concat({
                    [[             ]],
                    [[   █  █   ]],
                    [[   █ ██   ]],
                    [[   ████   ]],
                    [[   ██ ███   ]],
                    [[   █  █   ]],
                    [[             ]],
                    [[ n e o v i m ]],
                    [[             ]],
                }, '\n'),
            },
            { header_spec = table.concat({
                    [[                                                                       ]],
                    [[                                                                     ]],
                    [[       ████ ██████           █████      ██                     ]],
                    [[      ███████████             █████                             ]],
                    [[      █████████ ███████████████████ ███   ███████████   ]],
                    [[     █████████  ███    █████████████ █████ ██████████████   ]],
                    [[    █████████ ██████████ █████████ █████ █████ ████ █████   ]],
                    [[  ███████████ ███    ███ █████████ █████ █████ ████ █████  ]],
                    [[ ██████  █████████████████████ ████ █████ █████ ████ ██████ ]],
                    [[                                                                       ]],
                }, '\n'),
            },
        }

        -- Randomly select one of the headers
        math.randomseed(os.time())
        local selected_header = header_specs[math.random(#header_specs)]

        -- Base dashboard configuration
        local base_config = {
            enabled = true,
            preset = {
                header = selected_header,
                keys = {
                    { icon = " ", key = "f", desc = "Find File", action = ":lua Snacks.dashboard.pick('files')" },
                    { icon = " ", key = "n", desc = "New File", action = ":ene | startinsert" },
                    { icon = " ", key = "g", desc = "Find Text", action = ":lua Snacks.dashboard.pick('live_grep')" },
                    { icon = " ", key = "r", desc = "Recent Files", action = ":lua Snacks.dashboard.pick('oldfiles')" },
                    { icon = " ", key = "c", desc = "Config", action = ":lua Snacks.dashboard.pick('files', {cwd = vim.fn.stdpath('config')})" },
                    { icon = " ", key = "s", desc = "Restore Session", section = "session" },
                    { icon = "󰒲 ", key = "L", desc = "Lazy", action = ":Lazy", enabled = package.loaded.lazy ~= nil },
                    { icon = " ", key = "q", desc = "Quit", action = ":qa" },
                },
            },
        }
        require("snacks").setup({
            -- Global configuration
            bigfile = { enabled = true },
            notifier = { enabled = true },
            quickfile = { enabled = true },
            statuscolumn = { enabled = true },
            words = { enabled = true },

            -- Dashboard configuration
            dashboard = vim.tbl_deep_extend("force", base_config, selected_spec),
        })
    end

    -- Treesitter (keeping your existing configuration)
    require "nvim-treesitter.configs".setup({
        ensure_installed = {
            "c", "lua", "vim", "vimdoc", "query", "markdown", "markdown_inline",
            "javascript", "typescript", "tsx", "html", "css", "json", "yaml",
            "python", "rust", "go", "bash", "dockerfile", "gitignore",
            "svelte", "vue", "astro", "norg"
        },
        sync_install = false,
        auto_install = true,
        highlight = {
            enable = true,
            additional_vim_regex_highlighting = false,
        },
        indent = {
            enable = true
        },
        incremental_selection = {
            enable = true,
            keymaps = {
                init_selection = "gnn",
                node_incremental = "grn",
                scope_incremental = "grc",
                node_decremental = "grm",
            },
        },
        textobjects = {
            select = {
                enable = true,
                lookahead = true,
                keymaps = {
                    ["af"] = "@function.outer",
                    ["if"] = "@function.inner",
                    ["ac"] = "@class.outer",
                    ["ic"] = "@class.inner",
                },
            },
        },
    })

--
    -- LSP Configuration
    -- local lsp_zero = require('lsp-zero')

    -- lsp_zero.on_attach(function(client, bufnr)
    --     local opts = { buffer = bufnr, remap = false }

    --     vim.keymap.set("n", "gd", function() vim.lsp.buf.definition() end, opts)
    --     vim.keymap.set("n", "K", function() vim.lsp.buf.hover() end, opts)
    --     vim.keymap.set("n", "<leader>vws", function() vim.lsp.buf.workspace_symbol() end, opts)
    --     vim.keymap.set("n", "<leader>vd", function() vim.diagnostic.open_float() end, opts)
    --     vim.keymap.set("n", "[d", function() vim.diagnostic.goto_next() end, opts)
    --     vim.keymap.set("n", "]d", function() vim.diagnostic.goto_prev() end, opts)
    --     vim.keymap.set("n", "<leader>vca", function() vim.lsp.buf.code_action() end, opts)
    --     vim.keymap.set("n", "<leader>vrr", function() vim.lsp.buf.references() end, opts)
    --     vim.keymap.set("n", "<leader>vrn", function() vim.lsp.buf.rename() end, opts)
    --     vim.keymap.set("i", "<C-h>", function() vim.lsp.buf.signature_help() end, opts)
    -- end)

    -- require('mason').setup({})
    -- require('mason-lspconfig').setup({
    --     ensure_installed = { 'tsserver', 'rust_analyzer', 'lua_ls', 'pyright' },
    --     handlers = {
    --         lsp_zero.default_setup,
    --         lua_ls = function()
    --             local lua_opts = lsp_zero.nvim_lua_ls()
    --             require('lspconfig').lua_ls.setup(lua_opts)
    --         end,
    --     }
    -- })

    -- local cmp = require('cmp')
    -- local cmp_select = { behavior = cmp.SelectBehavior.Select }

    -- cmp.setup({
    --     sources = {
    --         { name = 'path' },
    --         { name = 'nvim_lsp' },
    --         { name = 'nvim_lua' },
    --         { name = 'luasnip', keyword_length = 2 },
    --         { name = 'buffer',  keyword_length = 3 },
    --     },
    --     formatting = lsp_zero.cmp_format(),
    --     mapping = cmp.mapping.preset.insert({
    --         ['<C-p>'] = cmp.mapping.select_prev_item(cmp_select),
    --         ['<C-n>'] = cmp.mapping.select_next_item(cmp_select),
    --         ['<C-y>'] = cmp.mapping.confirm({ select = true }),
    --         ["<C-Space>"] = cmp.mapping.complete(),
    --     }),
    -- })

    -- -- Telescope configuration
    -- local builtin = require('telescope.builtin')
    -- vim.keymap.set('n', '<leader>pf', builtin.find_files, {})
    -- vim.keymap.set('n', '<C-p>', builtin.git_files, {})
    -- vim.keymap.set('n', '<leader>ps', function()
    --     builtin.grep_string({ search = vim.fn.input("Grep > ") })
    -- end)
    -- vim.keymap.set('n', '<leader>vh', builtin.help_tags, {})

    -- -- Harpoon configuration
    -- local mark = require("harpoon.mark")
    -- local ui = require("harpoon.ui")

    -- vim.keymap.set("n", "<leader>a", mark.add_file)
    -- vim.keymap.set("n", "<C-e>", ui.toggle_quick_menu)

    -- vim.keymap.set("n", "<C-h>", function() ui.nav_file(1) end)
    -- vim.keymap.set("n", "<C-t>", function() ui.nav_file(2) end)
    -- vim.keymap.set("n", "<C-n>", function() ui.nav_file(3) end)
    -- vim.keymap.set("n", "<C-s>", function() ui.nav_file(4) end)

    -- -- Undotree configuration
    -- vim.keymap.set("n", "<leader>u", vim.cmd.UndotreeToggle)

    -- -- Fugitive configuration
    -- vim.keymap.set("n", "<leader>gs", vim.cmd.Git)

    -- -- Additional configurations
    -- vim.keymap.set("n", "<leader>f", vim.lsp.buf.format)

    -- vim.keymap.set("v", "J", ":m '>+1<CR>gv=gv")
    -- vim.keymap.set("v", "K", ":m '<-2<CR>gv=gv")

    -- vim.keymap.set("n", "J", "mzJ`z")
    -- vim.keymap.set("n", "<C-d>", "<C-d>zz")
    -- vim.keymap.set("n", "<C-u>", "<C-u>zz")
    -- vim.keymap.set("n", "n", "nzzzv")
    -- vim.keymap.set("n", "N", "Nzzzv")

    -- vim.keymap.set("x", "<leader>p", [["_dP]])

    -- vim.keymap.set({ "n", "v" }, "<leader>y", [["+y]])
    -- vim.keymap.set("n", "<leader>Y", [["+Y]])

    -- vim.keymap.set({ "n", "v" }, "<leader>d", [["_d]])

    -- vim.keymap.set("i", "<C-c>", "<Esc>")

    -- vim.keymap.set("n", "Q", "<nop>")
    -- vim.keymap.set("n", "<C-f>", "<cmd>silent !tmux neww tmux-sessionizer<CR>")

    -- vim.keymap.set("n", "<C-k>", "<cmd>cnext<CR>zz")
    -- vim.keymap.set("n", "<C-j>", "<cmd>cprev<CR>zz")
    -- vim.keymap.set("n", "<leader>k", "<cmd>lnext<CR>zz")
    -- vim.keymap.set("n", "<leader>j", "<cmd>lprev<CR>zz")

    -- vim.keymap.set("n", "<leader>s", [[:%s/\<<C-r><C-w>\>/<C-r><C-w>/gI<Left><Left><Left>]])
    -- vim.keymap.set("n", "<leader>x", "<cmd>!chmod +x %<CR>", { silent = true })

    -- vim.keymap.set("n", "<leader><leader>", function()
    --     vim.cmd("so")
    -- end)
end
