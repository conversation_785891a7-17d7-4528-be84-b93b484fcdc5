
-- ============================================================================
-- NEOVIM CONFIGURATION
-- ============================================================================

do -- Leader Key Configuration
    -- Set <SPACE> as the `global` leader key, <COMMA> as `local` leader key
    -- NOTE: Must happen before plugins are required (otherwise wrong leader will be used)
    vim.g.mapleader = ' '
    vim.g.maplocalleader = ','
end

do -- Plugin Installation and Management
    do -- echasnovski/mini.nvim bootstrap
        local path_package = vim.fn.stdpath('data') .. '/site'
        local mini_path = path_package .. '/pack/deps/start/mini.nvim'
        if not vim.loop.fs_stat(mini_path) then
        vim.cmd('echo "Installing `mini.nvim`" | redraw')
        local clone_cmd = {
            'git', 'clone', '--filter=blob:none',
            -- Uncomment next line to use 'stable' branch
            -- '--branch', 'stable',
            'https://github.com/echasnovski/mini.nvim', mini_path
        }
        vim.fn.system(clone_cmd)
        vim.cmd('packadd mini.nvim | helptags ALL')
        vim.cmd('echo "Installed `mini.nvim`" | redraw')
        end
    end

    vim.pack.add({ -- Plugin declarations
        { src = "https://github.com/vague2k/vague.nvim" },
        { src = "https://github.com/echasnovski/mini.nvim" },
        { src = "https://github.com/stevearc/oil.nvim" },
        { src = "https://github.com/nvim-lua/plenary.nvim" }, -- Required for telescope
        { src = "https://github.com/nvim-telescope/telescope.nvim" },
        { src = "https://github.com/nvim-telescope/telescope-fzf-native.nvim" }, -- Optional but recommended
        { src = "https://github.com/nvim-treesitter/nvim-treesitter" },
        { src = "https://github.com/neovim/nvim-lspconfig" },
        { src = "https://github.com/chomosuke/typst-preview.nvim" },
        { src = "https://github.com/saghen/blink.cmp", checkout = "v0.*" }, -- Modern completion engine with version pin
        -- UI and Dashboard
        { src = "https://github.com/folke/snacks.nvim" }, -- Modern UI components and dashboard
        { src = "https://github.com/folke/which-key.nvim" }, -- Key mapping helper
        { src = "https://github.com/MunifTanjim/nui.nvim" }, -- Required for Laravel.nvim
        { src = "https://github.com/kevinhwang91/promise-async" }, -- Required for Laravel.nvim

        -- Note taking and organization
        { src = "https://github.com/nvim-neorg/neorg" }, -- Note taking and organization (re-enabled with safer config)

        -- Telescope extensions
        { src = "https://github.com/jvgrootveld/telescope-zoxide" }, -- Zoxide integration
        { src = "https://github.com/nvim-neorg/neorg-telescope" }, -- Neorg telescope integration (re-enabled)
        { src = "https://github.com/gbirke/telescope-foldmarkers.nvim" }, -- Fold markers search (corrected URL)
        { src = "https://github.com/zschreur/telescope-jj.nvim" }, -- Jujutsu VCS integration (corrected URL)
        { src = "https://github.com/nvim-telescope/telescope-github.nvim" }, -- GitHub integration
        { src = "https://github.com/nvim-telescope/telescope-media-files.nvim" }, -- Media files preview
        { src = "https://github.com/nvim-telescope/telescope-fzf-writer.nvim" }, -- FZF writer
        { src = "https://github.com/nvim-telescope/telescope-symbols.nvim" }, -- Symbol picker
        { src = "https://github.com/olacin/telescope-cc.nvim" }, -- Conventional commits (corrected URL)
        { src = "https://github.com/sudormrfbin/cheatsheet.nvim" }, -- Cheatsheet
        { src = "https://github.com/nat-418/telescope-color-names.nvim" }, -- Color names (corrected URL)
        { src = "https://github.com/octarect/telescope-menu.nvim" }, -- Menu system
        { src = "https://github.com/debugloop/telescope-undo.nvim" }, -- Undo tree

        -- Debugging
        { src = "https://github.com/mfussenegger/nvim-dap" }, -- Debug adapter protocol
        { src = "https://github.com/nvim-telescope/telescope-dap.nvim" }, -- DAP telescope integration
        -- Themes
        { src = "https://github.com/catppuccin/nvim" },
        { src = "https://github.com/rebelot/kanagawa.nvim" },

        -- Laravel Development
        { src = "https://github.com/adalessa/laravel.nvim" }, -- Comprehensive Laravel plugin
        { src = "https://github.com/ricardoramirezr/blade-nav.nvim" }, -- Blade navigation and completion
        { src = "https://github.com/jwalton512/vim-blade" }, -- Blade syntax highlighting

        -- AI Assistance
        { src = "https://github.com/github/copilot.vim" }, -- GitHub Copilot
        { src = "https://github.com/augmentcode/augment.vim" }, -- Augment AI code suggestions
        { src = "https://github.com/Exafunction/codeium.nvim" }, -- Local AI and cloud AI support

        -- LM Studio / Local LLM Integration
        { src = "https://github.com/olimorris/codecompanion.nvim" }, -- AI coding companion with local LLM support
        { src = "https://github.com/David-Kunz/gen.nvim" }, -- Local AI text generation plugin
    })
end

do -- Telescope Configuration
    require('telescope').setup({
        defaults = {
            -- Default configuration for telescope goes here:
            mappings = {
                i = {
                    -- Insert mode mappings
                    ["<C-n>"] = "move_selection_next",
                    ["<C-p>"] = "move_selection_previous",
                    ["<C-c>"] = "close",
                    ["<C-j>"] = "move_selection_next",
                    ["<C-k>"] = "move_selection_previous",
                },
                n = {
                    -- Normal mode mappings
                    ["<esc>"] = "close",
                    ["j"] = "move_selection_next",
                    ["k"] = "move_selection_previous",
                    ["q"] = "close",
                },
            },
        },
        pickers = {
            find_files = {
                theme = "dropdown",
            }
        },
        extensions = {
            -- FZF native extension
            fzf = {
                fuzzy = true,
                override_generic_sorter = true,
                override_file_sorter = true,
                case_mode = "smart_case",
            },
            -- Media files extension
            media_files = {
                filetypes = {"png", "webp", "jpg", "jpeg"},
                find_cmd = "rg"
            },
            -- Undo extension
            undo = {
                use_delta = true,
                use_custom_command = nil,
                side_by_side = false,
                vim_diff_opts = { ctxlen = vim.o.scrolloff },
                entry_format = "state #$ID, $STAT, $TIME",
                time_format = "",
                saved_only = false,
            },
            -- Zoxide extension
            zoxide = {
                prompt_title = "[ Walking on the shoulders of TJ ]",
                mappings = {
                    default = {
                        after_action = function(selection)
                            print("Update to (" .. selection.z_score .. ") " .. selection.path)
                        end
                    },
                    ["<C-s>"] = {
                        before_action = function(selection) print("before C-s") end,
                        action = function(selection)
                            vim.cmd.edit(selection.path)
                        end
                    },
                    ["<C-q>"] = { action = "file_vsplit" },
                },
            },
        }
    })

    do -- Load telescope extensions
        pcall(require('telescope').load_extension, 'fzf')
        pcall(require('telescope').load_extension, 'zoxide')
        pcall(require('telescope').load_extension, 'neorg')
        pcall(require('telescope').load_extension, 'foldmarkers') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'jj') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'gh')
        pcall(require('telescope').load_extension, 'media_files')
        pcall(require('telescope').load_extension, 'fzf_writer')
        pcall(require('telescope').load_extension, 'symbols')
        pcall(require('telescope').load_extension, 'conventional_commits') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'cheatsheet')
        pcall(require('telescope').load_extension, 'color_names') -- Re-enabled with correct URL
        pcall(require('telescope').load_extension, 'menu')
        pcall(require('telescope').load_extension, 'undo')
        pcall(require('telescope').load_extension, 'dap')
    end

    do -- Telescope key mappings
        local builtin = require('telescope.builtin')

        -- Core telescope mappings
        vim.keymap.set('n', '<leader>ff', builtin.find_files, { desc = 'Find files' })
        vim.keymap.set('n', '<leader>fg', builtin.live_grep, { desc = 'Live grep' })
        vim.keymap.set('n', '<leader>fb', builtin.buffers, { desc = 'Buffers' })
        vim.keymap.set('n', '<leader>fh', builtin.help_tags, { desc = 'Help tags' })
        vim.keymap.set('n', '<leader>fr', builtin.oldfiles, { desc = 'Recent files' })
        vim.keymap.set('n', '<leader>fs', builtin.current_buffer_fuzzy_find, { desc = 'Search in current buffer' })
        vim.keymap.set('n', '<leader>fc', builtin.commands, { desc = 'Commands' })
        vim.keymap.set('n', '<leader>fk', builtin.keymaps, { desc = 'Keymaps' })
        vim.keymap.set('n', '<leader>f', builtin.find_files, { desc = 'Find files' })
        vim.keymap.set('n', '<leader>h', builtin.help_tags, { desc = 'Help tags' })

        -- Extension mappings
        vim.keymap.set('n', '<leader>tz', '<cmd>Telescope zoxide list<cr>', { desc = 'Zoxide' })
        vim.keymap.set('n', '<leader>tu', '<cmd>Telescope undo<cr>', { desc = 'Undo tree' })
        vim.keymap.set('n', '<leader>tm', '<cmd>Telescope media_files<cr>', { desc = 'Media files' })
        vim.keymap.set('n', '<leader>ts', '<cmd>Telescope symbols<cr>', { desc = 'Symbols' })
        vim.keymap.set('n', '<leader>tc', '<cmd>Telescope cheatsheet<cr>', { desc = 'Cheatsheet' })
        vim.keymap.set('n', '<leader>tC', '<cmd>Telescope color_names<cr>', { desc = 'Color names' }) -- Re-enabled with correct URL
        vim.keymap.set('n', '<leader>tM', '<cmd>Telescope menu<cr>', { desc = 'Menu' })
        vim.keymap.set('n', '<leader>tj', '<cmd>Telescope jj<cr>', { desc = 'Jujutsu VCS' }) -- New mapping for Jujutsu
        vim.keymap.set('n', '<leader>tf', '<cmd>Telescope foldmarkers<cr>', { desc = 'Fold markers' }) -- Re-enabled with correct URL
        vim.keymap.set('n', '<leader>tcc', '<cmd>Telescope conventional_commits<cr>', { desc = 'Conventional commits' }) -- New mapping

        -- Git/GitHub mappings
        vim.keymap.set('n', '<leader>gi', '<cmd>Telescope gh issues<cr>', { desc = 'GitHub issues' })
        vim.keymap.set('n', '<leader>gp', '<cmd>Telescope gh pull_request<cr>', { desc = 'GitHub PRs' })
        vim.keymap.set('n', '<leader>gr', '<cmd>Telescope gh run<cr>', { desc = 'GitHub runs' })

        -- Debug mappings
        vim.keymap.set('n', '<leader>dc', '<cmd>Telescope dap commands<cr>', { desc = 'DAP commands' })
        vim.keymap.set('n', '<leader>db', '<cmd>Telescope dap list_breakpoints<cr>', { desc = 'DAP breakpoints' })
        vim.keymap.set('n', '<leader>dv', '<cmd>Telescope dap variables<cr>', { desc = 'DAP variables' })
        vim.keymap.set('n', '<leader>df', '<cmd>Telescope dap frames<cr>', { desc = 'DAP frames' })

        -- Neorg mappings
        vim.keymap.set('n', '<leader>nf', '<cmd>Telescope neorg find_linkable<cr>', { desc = 'Find linkable' })
        vim.keymap.set('n', '<leader>nh', '<cmd>Telescope neorg search_headings<cr>', { desc = 'Search headings' })
        vim.keymap.set('n', '<leader>ni', '<cmd>Telescope neorg insert_link<cr>', { desc = 'Insert link' })
        vim.keymap.set('n', '<leader>nF', '<cmd>Telescope neorg insert_file_link<cr>', { desc = 'Insert file link' })
    end
end

do -- Editor Options and Settings
    vim.o.clipboard = "unnamedplus"
    vim.o.expandtab = true
    vim.o.number = true
    vim.o.relativenumber = true
    vim.o.signcolumn = "yes"
    vim.o.smartindent = true
    vim.o.softtabstop = 4
    vim.o.swapfile = false
    vim.o.tabstop = 4
    vim.o.termguicolors = true
    vim.o.winborder = "rounded"
    vim.o.wrap = true
end

do -- Key Mappings
    -- File operations
    vim.keymap.set('n', '<leader>o', ':update<CR> :source<CR>')
    vim.keymap.set('n', '<leader>w', ':write<CR>')
    vim.keymap.set('n', '<leader>q', ':quit<CR>')

    -- Clipboard operations
    vim.keymap.set({ 'n', 'v', 'x' }, '<leader>y', '"+y<CR>')
    vim.keymap.set({ 'n', 'v', 'x' }, '<leader>d', '"+d<CR>')

    -- File explorer
    vim.keymap.set('n', '<leader>e', ":Oil<CR>")

    -- LSP mappings
    vim.keymap.set('n', '<leader>lf', vim.lsp.buf.format)
end

do -- LSP Configuration
    vim.api.nvim_create_autocmd('LspAttach', {
        callback = function(ev)
            -- Blink.cmp handles completion, so we don't need to enable native completion
            local client = vim.lsp.get_client_by_id(ev.data.client_id)

            -- Optional: Add LSP-specific keymaps here
            local opts = { buffer = ev.buf }
            vim.keymap.set('n', 'gd', vim.lsp.buf.definition, opts)
            vim.keymap.set('n', 'K', vim.lsp.buf.hover, opts)
            vim.keymap.set('n', 'gi', vim.lsp.buf.implementation, opts)
            vim.keymap.set('n', '<C-k>', vim.lsp.buf.signature_help, opts)
            vim.keymap.set('n', '<leader>rn', vim.lsp.buf.rename, opts)
            vim.keymap.set('n', '<leader>ca', vim.lsp.buf.code_action, opts)
            vim.keymap.set('n', 'gr', vim.lsp.buf.references, opts)
        end,
    })

    -- Manual PHP LSP configuration for Devsense PHP LS
    vim.api.nvim_create_autocmd("FileType", {
        pattern = "php",
        callback = function()
            vim.lsp.start({
                name = "devsense-php-ls",
                cmd = { "devsense-php-ls" }, -- Adjust this if the command is different
                root_dir = vim.fs.dirname(vim.fs.find({"composer.json", ".git"}, { upward = true })[1]),
                settings = {
                    php = {
                        completion = {
                            enabled = true,
                        },
                        diagnostics = {
                            enabled = true,
                        },
                        format = {
                            enabled = true,
                        },
                    },
                },
            })
        end,
    })

    -- Enable LSP servers
    vim.lsp.enable({ "lua_ls", "biome", "tinymist", "emmetls", "phpactor" })
end

do -- Plugin Configurations

    -- Load essential plugins first
    vim.cmd('packadd plenary.nvim')
    vim.cmd('packadd nui.nvim')
    vim.cmd('packadd promise-async')

    -- Build blink.cmp from source if needed
    do -- Blink.cmp build check and setup
        local blink_path = vim.fn.stdpath('data') .. '/site/pack/deps/start/blink.cmp'
        local target_dir = blink_path .. '/target/release'

        -- Force rebuild if checkout version exists (cleanup old version)
        if vim.fn.isdirectory(blink_path) == 1 then
            local git_cmd = 'cd ' .. blink_path .. ' && git describe --tags 2>/dev/null'
            local current_ref = vim.fn.system(git_cmd)
            if string.match(current_ref, 'v0%.') then
                vim.cmd('echo "Removing old blink.cmp version..." | redraw')
                vim.fn.delete(blink_path, 'rf')
            end
        end

        -- Check if binary exists, if not build it
        if vim.fn.isdirectory(blink_path) == 1 and vim.fn.isdirectory(target_dir) == 0 then
            vim.cmd('echo "Building blink.cmp from source..." | redraw')
            local build_cmd = 'cd ' .. blink_path .. ' && cargo build --release'
            local result = vim.fn.system(build_cmd)
            if vim.v.shell_error == 0 then
                vim.cmd('echo "blink.cmp built successfully" | redraw')
            else
                vim.cmd('echo "Failed to build blink.cmp: ' .. result .. '" | redraw')
            end
        end
    end

    do -- Debug Adapter Protocol (DAP) configuration
        local dap = require('dap')

        -- Basic DAP configuration for common languages
        -- JavaScript/TypeScript (Node.js)
        dap.adapters.node2 = {
            type = 'executable',
            command = 'node',
            args = { vim.fn.stdpath('data') .. '/mason/packages/node-debug2-adapter/out/src/nodeDebug.js' },
        }

        dap.configurations.javascript = {
            {
                name = 'Launch',
                type = 'node2',
                request = 'launch',
                program = '${file}',
                cwd = vim.fn.getcwd(),
                sourceMaps = true,
                protocol = 'inspector',
                console = 'integratedTerminal',
            },
        }

        dap.configurations.typescript = dap.configurations.javascript
    end

    do -- Laravel configuration
        -- Load Laravel.nvim dependencies first
        vim.cmd('packadd nui.nvim')
        vim.cmd('packadd promise-async')

        -- Laravel.nvim configuration
        require("laravel").setup({
            lsp_server = "phpactor", -- Use phpactor as LSP server
            -- Use snacks picker since you have snacks.nvim configured
            default_picker = "snacks",
            -- Alternatively, use telescope since you have extensive telescope setup
            -- default_picker = "telescope",
        })

        -- Blade-nav.nvim configuration
        require("blade-nav").setup({
            -- This setting works for blink.cmp
            close_tag_on_complete = true, -- default: true
        })

        do -- Laravel key mappings
            -- Artisan commands
            vim.keymap.set('n', '<leader>aa', '<cmd>Laravel artisan<cr>', { desc = 'Artisan commands' })
            vim.keymap.set('n', '<leader>ar', '<cmd>Laravel routes<cr>', { desc = 'Laravel routes' })
            vim.keymap.set('n', '<leader>am', '<cmd>Laravel make<cr>', { desc = 'Laravel make' })

            -- Laravel development
            vim.keymap.set('n', '<leader>av', '<cmd>Laravel view_finder<cr>', { desc = 'View finder' })
            vim.keymap.set('n', '<leader>ac', '<cmd>Laravel composer<cr>', { desc = 'Composer commands' })
            vim.keymap.set('n', '<leader>at', '<cmd>Laravel tinker<cr>', { desc = 'Laravel Tinker' })

            -- Route navigation (blade-nav provides this)
            vim.keymap.set('n', '<leader>ao', '<cmd>Laravel route:open<cr>', { desc = 'Open route in browser' })
        end
    end

    -- Neorg configuration with safe loading to avoid circular dependencies
    do -- Neorg setup with error handling
        pcall(function()
            -- Only load neorg if it's available and properly installed
            local neorg_ok, neorg = pcall(require, "neorg")
            if neorg_ok then
                neorg.setup({
                    load = {
                        ["core.defaults"] = {},
                        ["core.concealer"] = {
                            config = {
                                icon_preset = "basic", -- Use basic icons to avoid font issues
                            }
                        },
                        ["core.dirman"] = {
                            config = {
                                workspaces = {
                                    notes = "~/notes",
                                    work = "~/work-notes",
                                },
                                default_workspace = "notes",
                            }
                        },
                        -- Skip completion module to avoid conflicts with blink.cmp
                        -- ["core.completion"] = {
                        --     config = {
                        --         engine = "nvim-cmp",
                        --     }
                        -- },
                        ["core.integrations.telescope"] = {},
                    },
                })
            end
        end)
    end

    -- Oil file manager
    require "oil".setup()

    -- Snacks.nvim - Modern UI components and comprehensive dashboard
    require("snacks").setup({
        -- Global configuration
        bigfile = { enabled = true },
        notifier = { enabled = true },
        quickfile = { enabled = true },
        statuscolumn = { enabled = true },
        words = { enabled = true },

        -- Dashboard configuration
        dashboard = {
            enabled = true,
            preset = {
                header = [[
  ███╗   ██╗███████╗ ██████╗ ██╗   ██╗██╗███╗   ███╗
  ████╗  ██║██╔════╝██╔═══██╗██║   ██║██║████╗ ████║
  ██╔██╗ ██║█████╗  ██║   ██║██║   ██║██║██╔████╔██║
  ██║╚██╗██║██╔══╝  ██║   ██║╚██╗ ██╔╝██║██║╚██╔╝██║
  ██║ ╚████║███████╗╚██████╔╝ ╚████╔╝ ██║██║ ╚═╝ ██║
  ╚═╝  ╚═══╝╚══════╝ ╚═════╝   ╚═══╝  ╚═╝╚═╝     ╚═╝
                ]],
                keys = {
                    { icon = " ", key = "f", desc = "Find File", action = ":lua Snacks.dashboard.pick('files')" },
                    { icon = " ", key = "n", desc = "New File", action = ":ene | startinsert" },
                    { icon = " ", key = "g", desc = "Find Text", action = ":lua Snacks.dashboard.pick('live_grep')" },
                    { icon = " ", key = "r", desc = "Recent Files", action = ":lua Snacks.dashboard.pick('oldfiles')" },
                    { icon = " ", key = "c", desc = "Config", action = ":lua Snacks.dashboard.pick('files', {cwd = vim.fn.stdpath('config')})" },
                    { icon = " ", key = "s", desc = "Restore Session", section = "session" },
                    { icon = "󰒲 ", key = "L", desc = "Lazy", action = ":Lazy", enabled = package.loaded.lazy ~= nil },
                    { icon = " ", key = "q", desc = "Quit", action = ":qa" },
                },
            },
            sections = {
                { section = "header" },
                { section = "keys", gap = 1, padding = 1 },
                { section = "startup" },
            },
        },
    })

    -- Treesitter (keeping your existing configuration)
    require "nvim-treesitter.configs".setup({
        ensure_installed = {
            "c", "lua", "vim", "vimdoc", "query", "markdown", "markdown_inline",
            "javascript", "typescript", "tsx", "html", "css", "json", "yaml",
            "python", "rust", "go", "bash", "dockerfile", "gitignore",
            "svelte", "vue", "astro", "norg"
        },
        sync_install = false,
        auto_install = true,
        ignore_install = { },
        highlight = {
            enable = true,
            disable = function(lang, buf)
                local max_filesize = 100 * 1024 -- 100 KB
                local ok, stats = pcall(vim.loop.fs_stat, vim.api.nvim_buf_get_name(buf))
                if ok and stats and stats.size > max_filesize then
                    return true
                end
            end,
            additional_vim_regex_highlighting = false,
        },
        incremental_selection = {
            enable = true,
            keymaps = {
                init_selection = "gnn",
                node_incremental = "grn",
                scope_incremental = "grc",
                node_decremental = "grm",
            },
        },
        indent = {
            enable = true,
            disable = { },
        },
        textobjects = {
            select = {
                enable = true,
                lookahead = true,
                keymaps = {
                    ["af"] = "@function.outer",
                    ["if"] = "@function.inner",
                    ["ac"] = "@class.outer",
                    ["ic"] = "@class.inner",
                },
            },
        },
    })

    -- Which-key configuration
    require("which-key").setup({
        preset = "modern",
        delay = 500,
        spec = {
            { "<leader>f", group = "Find" },
            { "<leader>l", group = "LSP" },
            { "<leader>d", group = "Debug" },
            { "<leader>n", group = "Notes" },
            { "<leader>g", group = "Git" },
            { "<leader>t", group = "Telescope" },
            { "<leader>a", group = "Laravel" }, -- Laravel group
            { "<leader>c", group = "AI/Copilot" }, -- AI group
            { "<leader>cg", group = "Gen AI" }, -- Gen.nvim subgroup
        },
    })

    do -- AI Assistants
        -- Augment.vim configuration
        do -- Augment AI settings and key mappings
            -- Augment key mappings
            vim.keymap.set('n', '<leader>cA', '<cmd>AugmentToggle<cr>', { desc = 'Toggle Augment' })
            vim.keymap.set('n', '<leader>cS', '<cmd>AugmentStatus<cr>', { desc = 'Augment status' })
            vim.keymap.set('n', '<leader>cC', '<cmd>AugmentConfig<cr>', { desc = 'Augment config' })

            -- Insert mode mappings for Augment (using different keys to avoid conflicts)
            vim.keymap.set('i', '<M-j>', '<Plug>(augment-next)', { desc = 'Next Augment suggestion' })
            vim.keymap.set('i', '<M-k>', '<Plug>(augment-previous)', { desc = 'Previous Augment suggestion' })
            vim.keymap.set('i', '<M-l>', '<Plug>(augment-accept)', { desc = 'Accept Augment suggestion' })
            vim.keymap.set('i', '<M-h>', '<Plug>(augment-dismiss)', { desc = 'Dismiss Augment suggestion' })
        end

        -- CodeCompanion.nvim - AI coding companion with LM Studio support
        do -- CodeCompanion configuration for LM Studio
            require("codecompanion").setup({
                display = {
                    diff = {
                        provider = "mini_diff", -- Use mini.diff for displaying diffs
                    },
                },
                opts = {
                    log_level = "DEBUG", -- or "TRACE" for more verbose logging
                },
                adapters = {
                    openai = function()
                        return require("codecompanion.adapters").extend("openai", {
                            env = {
                                api_key = "lm-studio", -- LM Studio doesn't require a real API key
                                url = "http://localhost:1234/v1", -- Default LM Studio URL
                            },
                        })
                    end,
                    -- Alternative: Set up as a custom adapter
                    lm_studio = function()
                        return require("codecompanion.adapters").extend("openai", {
                            name = "lm_studio",
                            env = {
                                api_key = "lm-studio",
                                url = "http://localhost:1234/v1",
                            },
                            headers = {
                                ["Content-Type"] = "application/json",
                            },
                            parameters = {
                                model = "local-model", -- This will be replaced by whatever model you load in LM Studio
                                max_tokens = 4096,
                                temperature = 0.1,
                                top_p = 1,
                                stream = true,
                            },
                        })
                    end,
                },
                strategies = {
                    chat = {
                        adapter = "lm_studio", -- Use LM Studio adapter for chat
                    },
                    inline = {
                        adapter = "lm_studio", -- Use LM Studio adapter for inline suggestions
                    },
                },
            })

            -- CodeCompanion key mappings
            vim.keymap.set({ "n", "v" }, "<leader>cL", "<cmd>CodeCompanionActions<cr>", { desc = "CodeCompanion Actions" })
            vim.keymap.set({ "n", "v" }, "<leader>cl", "<cmd>CodeCompanionChat Toggle<cr>", { desc = "CodeCompanion Chat" })
            vim.keymap.set("v", "<leader>ca", "<cmd>CodeCompanionAdd<cr>", { desc = "CodeCompanion Add" })

            -- Expand 'cc' into 'CodeCompanion' in the command line
            vim.cmd([[cab cc CodeCompanion]])
        end

        -- Codeium.nvim - Free AI completion with local support
        do -- Codeium configuration
            -- Configure Codeium with proper error handling for blink.cmp compatibility
            pcall(function()
                require("codeium").setup({
                    enable_chat = true,
                    -- Can be configured to use local endpoints if available
                })
            end)

            -- Codeium key mappings
            vim.keymap.set('i', '<C-g>', function () return vim.fn['codeium#Accept']() end, { expr = true, silent = true, desc = 'Accept Codeium suggestion' })
            vim.keymap.set('i', '<c-;>', function() return vim.fn['codeium#CycleCompletions'](1) end, { expr = true, silent = true, desc = 'Next Codeium suggestion' })
            vim.keymap.set('i', '<c-,>', function() return vim.fn['codeium#CycleCompletions'](-1) end, { expr = true, silent = true, desc = 'Previous Codeium suggestion' })
            vim.keymap.set('i', '<c-x>', function() return vim.fn['codeium#Clear']() end, { expr = true, silent = true, desc = 'Clear Codeium suggestion' })

            -- Normal mode Codeium commands
            vim.keymap.set('n', '<leader>cX', '<cmd>CodeiumToggle<cr>', { desc = 'Toggle Codeium' })
            vim.keymap.set('n', '<leader>cx', '<cmd>CodeiumChat<cr>', { desc = 'Codeium Chat' })
        end

        -- Gen.nvim - Local AI text generation
        do -- Gen.nvim configuration for LM Studio
            require('gen').setup({
                model = "local-model", -- Model name (will be replaced by whatever you load in LM Studio)
                host = "localhost", -- LM Studio host
                port = "1234", -- LM Studio port
                quit_map = "q", -- Set keymap to close the response window
                retry_map = "<c-r>", -- Set keymap to re-send the current prompt
                init = function(options) pcall(io.popen, "ollama serve > /dev/null 2>&1 &") end,
                -- Function to initialize Ollama (can be customized for LM Studio)
                command = function(options)
                    local body = {model = options.model, stream = true}
                    return "curl --silent --no-buffer -X POST http://" .. options.host .. ":" .. options.port .. "/api/generate -d $body"
                end,
                -- Custom prompts for LM Studio
                prompts = {
                    -- Built-in prompts
                    Summarize = "Summarize the following text:\n\n$text",
                    Translate = "Translate this text to $target:\n\n$text",
                    CodeReview = "Review the following code and suggest improvements:\n\nˋˋˋ$filetype\n$text\nˋˋˋ",
                    Explain = "Explain the following code:\n\nˋˋˋ$filetype\n$text\nˋˋˋ",
                    FixCode = "Fix the following code:\n\nˋˋˋ$filetype\n$text\nˋˋˋ",
                    OptimizeCode = "Optimize the following code:\n\nˋˋˋ$filetype\n$text\nˋˋˋ",
                    Docs = "Generate documentation for the following code:\n\nˋˋˋ$filetype\n$text\nˋˋˋ",
                    Tests = "Generate unit tests for the following code:\n\nˋˋˋ$filetype\n$text\nˋˋˋ",
                }
            })

            -- Gen.nvim key mappings
            vim.keymap.set({'n', 'v'}, '<leader>cg', ':Gen<CR>', { desc = 'Gen AI' })
            vim.keymap.set({'n', 'v'}, '<leader>cgs', ':Gen Summarize<CR>', { desc = 'Summarize' })
            vim.keymap.set({'n', 'v'}, '<leader>cgt', ':Gen Translate<CR>', { desc = 'Translate' })
            vim.keymap.set({'n', 'v'}, '<leader>cgr', ':Gen CodeReview<CR>', { desc = 'Code Review' })
            vim.keymap.set({'n', 'v'}, '<leader>cge', ':Gen Explain<CR>', { desc = 'Explain Code' })
            vim.keymap.set({'n', 'v'}, '<leader>cgf', ':Gen FixCode<CR>', { desc = 'Fix Code' })
            vim.keymap.set({'n', 'v'}, '<leader>cgo', ':Gen OptimizeCode<CR>', { desc = 'Optimize Code' })
            vim.keymap.set({'n', 'v'}, '<leader>cgd', ':Gen Docs<CR>', { desc = 'Generate Docs' })
            vim.keymap.set({'n', 'v'}, '<leader>cgT', ':Gen Tests<CR>', { desc = 'Generate Tests' })
        end

        -- GitHub Copilot configuration
        do -- Copilot settings and key mappings
            -- Enable Copilot for specific filetypes (optional)
            vim.g.copilot_filetypes = {
                ["*"] = true,
                ["dap-repl"] = false,
                ["dapui_watches"] = false,
                ["dapui_stacks"] = false,
                ["dapui_breakpoints"] = false,
                ["dapui_scopes"] = false,
                ["dapui_console"] = false,
            }

            -- Copilot key mappings
            vim.keymap.set('i', '<C-J>', '<Plug>(copilot-next)', { desc = 'Next Copilot suggestion' })
            vim.keymap.set('i', '<C-K>', '<Plug>(copilot-previous)', { desc = 'Previous Copilot suggestion' })
            vim.keymap.set('i', '<C-L>', '<Plug>(copilot-accept-word)', { desc = 'Accept Copilot word' })
            vim.keymap.set('i', '<C-H>', '<Plug>(copilot-dismiss)', { desc = 'Dismiss Copilot suggestion' })

            -- Normal mode Copilot commands
            vim.keymap.set('n', '<leader>ce', '<cmd>Copilot enable<cr>', { desc = 'Enable Copilot' })
            vim.keymap.set('n', '<leader>cd', '<cmd>Copilot disable<cr>', { desc = 'Disable Copilot' })
            vim.keymap.set('n', '<leader>cs', '<cmd>Copilot status<cr>', { desc = 'Copilot status' })
            vim.keymap.set('n', '<leader>cp', '<cmd>Copilot panel<cr>', { desc = 'Copilot panel' })
        end
    end
end

do -- Theme and Appearance
    require("catppuccin").setup({
        flavour = "mocha", -- latte, frappe, macchiato, mocha
        background = {
            light = "latte",
            dark = "mocha",
        },
        transparent_background = true, -- matches your current setup
    })
    require('kanagawa').setup({
        theme = "wave", -- wave, dragon, lotus
        background = {
            dark = "dragon",  -- wave or dragon
            light = "lotus"
        },
        transparent = true, -- matches your current setup
    })
    require "vague".setup({ transparent = true })

    vim.cmd("colorscheme kanagawa")
    vim.cmd(":hi statusline guibg=NONE")
end
